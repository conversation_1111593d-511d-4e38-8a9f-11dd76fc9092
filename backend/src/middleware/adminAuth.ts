import { Request, Response, NextFunction } from 'express';
import { AdminWhitelistModel } from '../database/models/AdminWhitelist.js';
import { AdminActivityLogModel } from '../database/models/AdminActivityLog.js';
import type { ApiResponse } from '../../../shared/types.js';

// Extend Request interface to include admin info
declare global {
  namespace Express {
    interface Request {
      admin?: {
        email: string;
        role: 'admin' | 'super_admin' | 'viewer';
        permissions: Record<string, any>;
      };
    }
  }
}

export interface AdminAuthOptions {
  requiredRole?: 'admin' | 'super_admin' | 'viewer';
  requiredPermission?: string;
  logActivity?: boolean;
  action?: string;
  resource?: string;
}

/**
 * Middleware to check if user is an admin based on email whitelist
 */
export const requireAdmin = (options: AdminAuthOptions = {}) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const user = req.user;
      
      if (!user || !user.email) {
        res.status(401).json({
          success: false,
          error: {
            code: 'AUTHENTICATION_REQUIRED',
            message: 'Authentication required'
          },
          timestamp: new Date()
        } as ApiResponse);
        return;
      }

      // Check if email is whitelisted
      const adminEntry = await AdminWhitelistModel.getByEmail(user.email);
      
      if (!adminEntry || !adminEntry.isActive) {
        // Log unauthorized access attempt
        await AdminActivityLogModel.log({
          adminEmail: user.email,
          action: 'unauthorized_access_attempt',
          resource: options.resource || req.path,
          details: {
            method: req.method,
            path: req.path,
            userAgent: req.get('User-Agent'),
            isWhitelisted: !!adminEntry,
            isActive: adminEntry?.isActive || false
          },
          ipAddress: req.ip,
          userAgent: req.get('User-Agent')
        });

        res.status(403).json({
          success: false,
          error: {
            code: 'ADMIN_ACCESS_REQUIRED',
            message: 'Admin access required. Please contact an administrator.'
          },
          timestamp: new Date()
        } as ApiResponse);
        return;
      }

      // Check role requirements
      if (options.requiredRole) {
        const roleHierarchy = { viewer: 1, admin: 2, super_admin: 3 };
        const userRoleLevel = roleHierarchy[adminEntry.role];
        const requiredRoleLevel = roleHierarchy[options.requiredRole];

        if (userRoleLevel < requiredRoleLevel) {
          await AdminActivityLogModel.log({
            adminEmail: user.email,
            action: 'insufficient_role_access_attempt',
            resource: options.resource || req.path,
            details: {
              userRole: adminEntry.role,
              requiredRole: options.requiredRole,
              method: req.method,
              path: req.path
            },
            ipAddress: req.ip,
            userAgent: req.get('User-Agent')
          });

          res.status(403).json({
            success: false,
            error: {
              code: 'INSUFFICIENT_ROLE',
              message: `${options.requiredRole} role required`
            },
            timestamp: new Date()
          } as ApiResponse);
          return;
        }
      }

      // Check specific permission requirements
      if (options.requiredPermission) {
        const hasPermission = adminEntry.permissions[options.requiredPermission] === true;
        
        if (!hasPermission) {
          await AdminActivityLogModel.log({
            adminEmail: user.email,
            action: 'insufficient_permission_access_attempt',
            resource: options.resource || req.path,
            details: {
              requiredPermission: options.requiredPermission,
              userPermissions: adminEntry.permissions,
              method: req.method,
              path: req.path
            },
            ipAddress: req.ip,
            userAgent: req.get('User-Agent')
          });

          res.status(403).json({
            success: false,
            error: {
              code: 'INSUFFICIENT_PERMISSION',
              message: `Permission '${options.requiredPermission}' required`
            },
            timestamp: new Date()
          } as ApiResponse);
          return;
        }
      }

      // Update last access time
      await AdminWhitelistModel.updateLastAccess(user.email);

      // Log activity if requested
      if (options.logActivity !== false) { // Default to true
        await AdminActivityLogModel.log({
          adminEmail: user.email,
          action: options.action || `${req.method.toLowerCase()}_${options.resource || req.path.replace(/\//g, '_')}`,
          resource: options.resource || req.path,
          details: {
            method: req.method,
            path: req.path,
            query: req.query,
            userRole: adminEntry.role
          },
          ipAddress: req.ip,
          userAgent: req.get('User-Agent')
        });
      }

      // Attach admin info to request
      req.admin = {
        email: adminEntry.email,
        role: adminEntry.role,
        permissions: adminEntry.permissions
      };

      next();
    } catch (error) {
      console.error('Admin authentication error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'ADMIN_AUTH_ERROR',
          message: 'Internal authentication error'
        },
        timestamp: new Date()
      } as ApiResponse);
    }
  };
};

/**
 * Middleware specifically for super admin actions
 */
export const requireSuperAdmin = requireAdmin({ 
  requiredRole: 'super_admin',
  action: 'super_admin_access',
  resource: 'super_admin_area'
});

/**
 * Middleware for viewer-level access (read-only)
 */
export const requireViewer = requireAdmin({ 
  requiredRole: 'viewer',
  action: 'viewer_access',
  resource: 'analytics_view'
});

/**
 * Middleware for admin management actions
 */
export const requireAdminManagement = requireAdmin({ 
  requiredPermission: 'canManageAdmins',
  action: 'admin_management',
  resource: 'admin_whitelist'
});

/**
 * Middleware for data export actions
 */
export const requireDataExport = requireAdmin({ 
  requiredPermission: 'canExportData',
  action: 'data_export',
  resource: 'analytics_data'
});

/**
 * Middleware for system management actions
 */
export const requireSystemManagement = requireAdmin({ 
  requiredPermission: 'canManageSystem',
  action: 'system_management',
  resource: 'system_config'
});

/**
 * Initialize default admin if none exist
 */
export const initializeDefaultAdmin = async (email: string): Promise<void> => {
  try {
    const stats = await AdminWhitelistModel.getStats();
    if (stats.totalAdmins === 0) {
      await AdminWhitelistModel.initializeDefaultAdmin(email);
      console.log(`✅ Initialized default admin: ${email}`);
    }
  } catch (error) {
    console.error('Failed to initialize default admin:', error);
  }
};

export default {
  requireAdmin,
  requireSuperAdmin,
  requireViewer,
  requireAdminManagement,
  requireDataExport,
  requireSystemManagement,
  initializeDefaultAdmin
};
