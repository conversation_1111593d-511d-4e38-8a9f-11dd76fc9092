import { query } from '../config.js';

export interface AdminActivityLogEntry {
  id: string;
  adminEmail: string;
  action: string;
  resource?: string;
  resourceId?: string;
  details?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
  sessionId?: string;
}

export interface CreateAdminActivityLogEntry {
  adminEmail: string;
  action: string;
  resource?: string;
  resourceId?: string;
  details?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
}

export interface AdminActivityLogFilter {
  adminEmail?: string;
  action?: string;
  resource?: string;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

export class AdminActivityLogModel {
  /**
   * Log an admin activity
   */
  static async log(data: CreateAdminActivityLogEntry): Promise<AdminActivityLogEntry> {
    const result = await query(
      `INSERT INTO admin_activity_log 
       (admin_email, action, resource, resource_id, details, ip_address, user_agent, session_id)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
       RETURNING id, admin_email, action, resource, resource_id, details, ip_address, user_agent, timestamp, session_id`,
      [
        data.adminEmail.toLowerCase(),
        data.action,
        data.resource,
        data.resourceId,
        data.details ? JSON.stringify(data.details) : null,
        data.ipAddress,
        data.userAgent,
        data.sessionId
      ]
    );

    const row = result.rows[0];
    return {
      id: row.id,
      adminEmail: row.admin_email,
      action: row.action,
      resource: row.resource,
      resourceId: row.resource_id,
      details: row.details || {},
      ipAddress: row.ip_address,
      userAgent: row.user_agent,
      timestamp: row.timestamp,
      sessionId: row.session_id
    };
  }

  /**
   * Get admin activity logs with filtering
   */
  static async getActivities(filter: AdminActivityLogFilter = {}): Promise<{
    activities: AdminActivityLogEntry[];
    total: number;
  }> {
    const conditions: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    if (filter.adminEmail) {
      conditions.push(`admin_email = $${paramIndex++}`);
      values.push(filter.adminEmail.toLowerCase());
    }

    if (filter.action) {
      conditions.push(`action = $${paramIndex++}`);
      values.push(filter.action);
    }

    if (filter.resource) {
      conditions.push(`resource = $${paramIndex++}`);
      values.push(filter.resource);
    }

    if (filter.startDate) {
      conditions.push(`timestamp >= $${paramIndex++}`);
      values.push(filter.startDate);
    }

    if (filter.endDate) {
      conditions.push(`timestamp <= $${paramIndex++}`);
      values.push(filter.endDate);
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    
    // Get total count
    const countResult = await query(
      `SELECT COUNT(*) as count FROM admin_activity_log ${whereClause}`,
      values
    );
    const total = parseInt(countResult.rows[0].count);

    // Get activities with pagination
    const limit = filter.limit || 50;
    const offset = filter.offset || 0;
    
    const activitiesResult = await query(
      `SELECT 
        id, admin_email, action, resource, resource_id, details, 
        ip_address, user_agent, timestamp, session_id
      FROM admin_activity_log 
      ${whereClause}
      ORDER BY timestamp DESC
      LIMIT $${paramIndex++} OFFSET $${paramIndex++}`,
      [...values, limit, offset]
    );

    const activities = activitiesResult.rows.map((row: any) => ({
      id: row.id,
      adminEmail: row.admin_email,
      action: row.action,
      resource: row.resource,
      resourceId: row.resource_id,
      details: row.details || {},
      ipAddress: row.ip_address,
      userAgent: row.user_agent,
      timestamp: row.timestamp,
      sessionId: row.session_id
    }));

    return { activities, total };
  }

  /**
   * Get recent activities for a specific admin
   */
  static async getRecentActivities(adminEmail: string, limit = 20): Promise<AdminActivityLogEntry[]> {
    const result = await query(
      `SELECT 
        id, admin_email, action, resource, resource_id, details, 
        ip_address, user_agent, timestamp, session_id
      FROM admin_activity_log 
      WHERE admin_email = $1
      ORDER BY timestamp DESC
      LIMIT $2`,
      [adminEmail.toLowerCase(), limit]
    );

    return result.rows.map((row: any) => ({
      id: row.id,
      adminEmail: row.admin_email,
      action: row.action,
      resource: row.resource,
      resourceId: row.resource_id,
      details: row.details || {},
      ipAddress: row.ip_address,
      userAgent: row.user_agent,
      timestamp: row.timestamp,
      sessionId: row.session_id
    }));
  }

  /**
   * Get activity statistics
   */
  static async getStats(days = 30): Promise<{
    totalActivities: number;
    uniqueAdmins: number;
    topActions: Array<{ action: string; count: number }>;
    dailyActivity: Array<{ date: string; count: number }>;
    adminActivity: Array<{ adminEmail: string; count: number }>;
  }> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    const [totalResult, uniqueResult, actionsResult, dailyResult, adminResult] = await Promise.all([
      query(
        'SELECT COUNT(*) as count FROM admin_activity_log WHERE timestamp >= $1',
        [cutoffDate]
      ),
      query(
        'SELECT COUNT(DISTINCT admin_email) as count FROM admin_activity_log WHERE timestamp >= $1',
        [cutoffDate]
      ),
      query(
        `SELECT action, COUNT(*) as count 
         FROM admin_activity_log 
         WHERE timestamp >= $1 
         GROUP BY action 
         ORDER BY count DESC 
         LIMIT 10`,
        [cutoffDate]
      ),
      query(
        `SELECT DATE(timestamp) as date, COUNT(*) as count 
         FROM admin_activity_log 
         WHERE timestamp >= $1 
         GROUP BY DATE(timestamp) 
         ORDER BY date DESC`,
        [cutoffDate]
      ),
      query(
        `SELECT admin_email, COUNT(*) as count 
         FROM admin_activity_log 
         WHERE timestamp >= $1 
         GROUP BY admin_email 
         ORDER BY count DESC 
         LIMIT 10`,
        [cutoffDate]
      )
    ]);

    return {
      totalActivities: parseInt(totalResult.rows[0].count),
      uniqueAdmins: parseInt(uniqueResult.rows[0].count),
      topActions: actionsResult.rows.map(row => ({
        action: row.action,
        count: parseInt(row.count)
      })),
      dailyActivity: dailyResult.rows.map(row => ({
        date: row.date.toISOString().split('T')[0],
        count: parseInt(row.count)
      })),
      adminActivity: adminResult.rows.map(row => ({
        adminEmail: row.admin_email,
        count: parseInt(row.count)
      }))
    };
  }

  /**
   * Clean up old activity logs (for data retention)
   */
  static async cleanup(retentionDays = 90): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    const result = await query(
      'DELETE FROM admin_activity_log WHERE timestamp < $1',
      [cutoffDate]
    );

    return result.rowCount;
  }

  /**
   * Get security-related activities
   */
  static async getSecurityActivities(days = 7): Promise<AdminActivityLogEntry[]> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    const securityActions = [
      'admin_login',
      'admin_logout',
      'whitelist_add',
      'whitelist_remove',
      'whitelist_update',
      'permission_change',
      'data_export',
      'system_config_change'
    ];

    const result = await query(
      `SELECT 
        id, admin_email, action, resource, resource_id, details, 
        ip_address, user_agent, timestamp, session_id
      FROM admin_activity_log 
      WHERE timestamp >= $1 AND action = ANY($2)
      ORDER BY timestamp DESC`,
      [cutoffDate, securityActions]
    );

    return result.rows.map(row => ({
      id: row.id,
      adminEmail: row.admin_email,
      action: row.action,
      resource: row.resource,
      resourceId: row.resource_id,
      details: row.details || {},
      ipAddress: row.ip_address,
      userAgent: row.user_agent,
      timestamp: row.timestamp,
      sessionId: row.session_id
    }));
  }
}

export default AdminActivityLogModel;
