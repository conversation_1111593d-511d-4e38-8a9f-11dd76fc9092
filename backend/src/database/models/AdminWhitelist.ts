import { query } from '../config.js';

export interface AdminWhitelistEntry {
  id: string;
  email: string;
  role: 'admin' | 'super_admin' | 'viewer';
  addedBy?: string;
  addedAt: Date;
  isActive: boolean;
  lastAccess?: Date;
  permissions: Record<string, any>;
  notes?: string;
}

export interface CreateAdminWhitelistEntry {
  email: string;
  role?: 'admin' | 'super_admin' | 'viewer';
  addedBy?: string;
  permissions?: Record<string, any>;
  notes?: string;
}

export interface UpdateAdminWhitelistEntry {
  role?: 'admin' | 'super_admin' | 'viewer';
  isActive?: boolean;
  permissions?: Record<string, any>;
  notes?: string;
}

export class AdminWhitelistModel {
  /**
   * Check if an email is whitelisted and active
   */
  static async isWhitelisted(email: string): Promise<boolean> {
    const result = await query(
      'SELECT id FROM admin_whitelist WHERE email = $1 AND is_active = true',
      [email.toLowerCase()]
    );
    return result.rows.length > 0;
  }

  /**
   * Get admin whitelist entry by email
   */
  static async getByEmail(email: string): Promise<AdminWhitelistEntry | null> {
    const result = await query(
      `SELECT 
        id, email, role, added_by, added_at, is_active, 
        last_access, permissions, notes
      FROM admin_whitelist 
      WHERE email = $1`,
      [email.toLowerCase()]
    );

    if (result.rows.length === 0) {
      return null;
    }

    const row = result.rows[0];
    return {
      id: row.id,
      email: row.email,
      role: row.role,
      addedBy: row.added_by,
      addedAt: row.added_at,
      isActive: row.is_active,
      lastAccess: row.last_access,
      permissions: row.permissions || {},
      notes: row.notes
    };
  }

  /**
   * Add a new admin to whitelist
   */
  static async add(data: CreateAdminWhitelistEntry): Promise<AdminWhitelistEntry> {
    const result = await query(
      `INSERT INTO admin_whitelist (email, role, added_by, permissions, notes)
       VALUES ($1, $2, $3, $4, $5)
       RETURNING id, email, role, added_by, added_at, is_active, last_access, permissions, notes`,
      [
        data.email.toLowerCase(),
        data.role || 'admin',
        data.addedBy,
        JSON.stringify(data.permissions || {}),
        data.notes
      ]
    );

    const row = result.rows[0];
    return {
      id: row.id,
      email: row.email,
      role: row.role,
      addedBy: row.added_by,
      addedAt: row.added_at,
      isActive: row.is_active,
      lastAccess: row.last_access,
      permissions: row.permissions || {},
      notes: row.notes
    };
  }

  /**
   * Update admin whitelist entry
   */
  static async update(email: string, data: UpdateAdminWhitelistEntry): Promise<AdminWhitelistEntry | null> {
    const setParts: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    if (data.role !== undefined) {
      setParts.push(`role = $${paramIndex++}`);
      values.push(data.role);
    }

    if (data.isActive !== undefined) {
      setParts.push(`is_active = $${paramIndex++}`);
      values.push(data.isActive);
    }

    if (data.permissions !== undefined) {
      setParts.push(`permissions = $${paramIndex++}`);
      values.push(JSON.stringify(data.permissions));
    }

    if (data.notes !== undefined) {
      setParts.push(`notes = $${paramIndex++}`);
      values.push(data.notes);
    }

    if (setParts.length === 0) {
      return this.getByEmail(email);
    }

    values.push(email.toLowerCase());
    const result = await query(
      `UPDATE admin_whitelist 
       SET ${setParts.join(', ')}
       WHERE email = $${paramIndex}
       RETURNING id, email, role, added_by, added_at, is_active, last_access, permissions, notes`,
      values
    );

    if (result.rows.length === 0) {
      return null;
    }

    const row = result.rows[0];
    return {
      id: row.id,
      email: row.email,
      role: row.role,
      addedBy: row.added_by,
      addedAt: row.added_at,
      isActive: row.is_active,
      lastAccess: row.last_access,
      permissions: row.permissions || {},
      notes: row.notes
    };
  }

  /**
   * Update last access time
   */
  static async updateLastAccess(email: string): Promise<void> {
    await query(
      'UPDATE admin_whitelist SET last_access = CURRENT_TIMESTAMP WHERE email = $1',
      [email.toLowerCase()]
    );
  }

  /**
   * Remove admin from whitelist
   */
  static async remove(email: string): Promise<boolean> {
    const result = await query(
      'DELETE FROM admin_whitelist WHERE email = $1',
      [email.toLowerCase()]
    );
    return result.rowCount > 0;
  }

  /**
   * Get all admin whitelist entries
   */
  static async getAll(includeInactive = false): Promise<AdminWhitelistEntry[]> {
    const whereClause = includeInactive ? '' : 'WHERE is_active = true';
    const result = await query(
      `SELECT 
        id, email, role, added_by, added_at, is_active, 
        last_access, permissions, notes
      FROM admin_whitelist 
      ${whereClause}
      ORDER BY added_at DESC`
    );

    return result.rows.map(row => ({
      id: row.id,
      email: row.email,
      role: row.role,
      addedBy: row.added_by,
      addedAt: row.added_at,
      isActive: row.is_active,
      lastAccess: row.last_access,
      permissions: row.permissions || {},
      notes: row.notes
    }));
  }

  /**
   * Get admin statistics
   */
  static async getStats(): Promise<{
    totalAdmins: number;
    activeAdmins: number;
    recentlyActive: number;
    roleDistribution: Record<string, number>;
  }> {
    const [totalResult, activeResult, recentResult, roleResult] = await Promise.all([
      query('SELECT COUNT(*) as count FROM admin_whitelist'),
      query('SELECT COUNT(*) as count FROM admin_whitelist WHERE is_active = true'),
      query('SELECT COUNT(*) as count FROM admin_whitelist WHERE last_access >= NOW() - INTERVAL \'7 days\''),
      query('SELECT role, COUNT(*) as count FROM admin_whitelist WHERE is_active = true GROUP BY role')
    ]);

    const roleDistribution: Record<string, number> = {};
    roleResult.rows.forEach(row => {
      roleDistribution[row.role] = parseInt(row.count);
    });

    return {
      totalAdmins: parseInt(totalResult.rows[0].count),
      activeAdmins: parseInt(activeResult.rows[0].count),
      recentlyActive: parseInt(recentResult.rows[0].count),
      roleDistribution
    };
  }

  /**
   * Initialize default admin (for first-time setup)
   */
  static async initializeDefaultAdmin(email: string): Promise<AdminWhitelistEntry> {
    // Check if any admins exist
    const existingResult = await query('SELECT COUNT(*) as count FROM admin_whitelist');
    const existingCount = parseInt(existingResult.rows[0].count);

    if (existingCount > 0) {
      throw new Error('Admin whitelist already has entries. Cannot initialize default admin.');
    }

    return this.add({
      email: email.toLowerCase(),
      role: 'super_admin',
      permissions: {
        canManageAdmins: true,
        canViewAllAnalytics: true,
        canExportData: true,
        canManageSystem: true
      },
      notes: 'Default admin - initialized automatically'
    });
  }
}

export default AdminWhitelistModel;
