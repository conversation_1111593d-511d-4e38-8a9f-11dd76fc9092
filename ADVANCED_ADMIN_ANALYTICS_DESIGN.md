# 📊 **ORA Advanced Analytics Dashboard - Complete Documentation**

## 🎯 **Overview**

The ORA Analytics Dashboard is a comprehensive business intelligence platform designed specifically for emotion-AI applications. It provides deep insights into user behavior, emotional patterns, system performance, and predictive analytics to help you understand and optimize your voice AI product.

---

## 🏗️ **Architecture & Data Storage**

### **Database Schema**

The analytics system uses **PostgreSQL** with the following key tables:

#### **Core Data Tables:**
```sql
-- User data and sessions
users (id, email, name, age_range, persona_type, created_at)
chat_sessions (id, user_id, started_at, ended_at, session_data)
conversation_messages (id, session_id, content, emotions, prosody_scores, timestamp)

-- Emotion analytics (derived from conversation_messages)
emotion_analytics (id, user_id, session_id, emotion, score, timestamp)

-- Voice storage tracking
voice_storage (id, user_id, session_id, file_path, file_size, storage_type, created_at)
```

#### **Admin Management Tables:**
```sql
-- Admin access control
admin_whitelist (
  email VARCHAR PRIMARY KEY,
  role <PERSON><PERSON><PERSON><PERSON> DEFAULT 'admin',
  is_active BOOLEAN DEFAULT true,
  permissions JSONB DEFAULT '{}',
  notes TEXT,
  added_by VARCHAR,
  added_at TIMESTAMP DEFAULT NOW(),
  last_active_at TIMESTAMP
);

-- Security audit trail
admin_activity_log (
  id UUID PRIMARY KEY,
  admin_email VARCHAR,
  action VARCHAR,
  resource VARCHAR,
  details JSONB,
  ip_address INET,
  user_agent TEXT,
  timestamp TIMESTAMP DEFAULT NOW()
);
```

### **Data Flow**
1. **Real-time Collection**: Hume EVI conversations → `conversation_messages` with emotion scores
2. **Processing**: Background jobs extract emotion data → `emotion_analytics` table
3. **Analytics**: Advanced queries aggregate data for dashboard insights
4. **Caching**: Results cached for performance (30-second refresh cycles)

---

## 📈 **Analytics Capabilities**

### **1. Overview Dashboard**
- **Total Users**: Count from `users` table
- **Active Sessions**: Real-time count from `chat_sessions` 
- **Total Messages**: Count from `conversation_messages`
- **Emotion Capture Rate**: Percentage of messages with emotion data

### **2. Emotion Intelligence**
#### **Emotion Correlations**
```sql
-- Discovers which emotions appear together
WITH emotion_pairs AS (
  SELECT e1.emotion, e2.emotion, e1.score, e2.score
  FROM emotion_analytics e1
  JOIN emotion_analytics e2 ON e1.session_id = e2.session_id 
    AND e1.timestamp = e2.timestamp 
    AND e1.emotion < e2.emotion
)
SELECT emotion1, emotion2, CORR(score1, score2) as correlation
FROM emotion_pairs GROUP BY emotion1, emotion2
```

#### **Anomaly Detection**
- Compares user's current emotions against their historical baseline
- Identifies deviations >2 standard deviations
- Flags potential emotional concerns or unusual patterns

### **3. User Cohort Analysis**
```sql
-- Groups users by signup month and tracks retention
WITH user_stats AS (
  SELECT 
    u.id,
    DATE_TRUNC('month', u.created_at) as signup_month,
    COUNT(DISTINCT cs.id) as session_count,
    MAX(cs.started_at) as last_session_at
  FROM users u
  LEFT JOIN chat_sessions cs ON u.id = cs.user_id
  GROUP BY u.id, u.created_at
)
SELECT 
  signup_month,
  COUNT(*) as user_count,
  COUNT(CASE WHEN last_session_at >= NOW() - INTERVAL '30 days' 
        THEN 1 END)::float / COUNT(*) as retention_rate
FROM user_stats GROUP BY signup_month
```

### **4. Real-time Emotion Flow**
- **Current Trends**: Hourly emotion score changes (rising/falling/stable)
- **Peak Detection**: Identifies highest emotion intensities and timing
- **Transition Analysis**: Maps common emotion-to-emotion transitions

### **5. Predictive Analytics**
#### **Churn Risk Prediction**
```sql
-- Identifies users likely to stop using the platform
WITH user_activity AS (
  SELECT 
    u.id,
    COUNT(cs.id) as total_sessions,
    MAX(cs.started_at) as last_session,
    COUNT(CASE WHEN cs.started_at >= NOW() - INTERVAL '7 days' 
          THEN 1 END) as recent_sessions
  FROM users u
  LEFT JOIN chat_sessions cs ON u.id = cs.user_id
  GROUP BY u.id
)
SELECT user_id, 
  CASE 
    WHEN recent_sessions = 0 AND last_session < NOW() - INTERVAL '14 days' THEN 0.9
    WHEN recent_sessions <= 1 THEN 0.7
    ELSE 0.2
  END as churn_probability
FROM user_activity
```

### **6. User Segmentation**
Automatically categorizes users into segments:
- **Power Users**: 20+ sessions, 10+ min average duration
- **Engaged Users**: 5+ sessions, high positive emotions
- **At-Risk Users**: ≤2 sessions, inactive >7 days
- **Casual Users**: 3+ sessions, <5 min duration
- **New Users**: Default category

---

## 🔐 **Admin User Management**

### **Adding New Admin Users**

#### **Method 1: Through Dashboard (Recommended)**
1. Navigate to `/admin/analytics`
2. Click "Admin Management" tab
3. Click "Add Admin" button
4. Fill out the form:
   - **Email**: Must be valid email address
   - **Role**: Choose from admin/super_admin/viewer
   - **Notes**: Optional description
5. Click "Add Admin"

#### **Method 2: Direct Database Insert**
```sql
INSERT INTO admin_whitelist (email, role, added_by, notes) 
VALUES ('<EMAIL>', 'admin', 'system', 'Added via SQL');
```

#### **Method 3: API Endpoint**
```bash
curl -X POST http://localhost:5000/api/admin/whitelist \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "email": "<EMAIL>",
    "role": "admin",
    "notes": "Added via API"
  }'
```

### **Admin Roles & Permissions**

| Role | Permissions | Description |
|------|-------------|-------------|
| **viewer** | Read-only access to analytics | Can view dashboards but cannot modify |
| **admin** | Full analytics + user management | Can view all data and manage other admins |
| **super_admin** | All permissions + system settings | Complete control over the platform |

### **Security Features**
- **Activity Logging**: All admin actions logged with IP, timestamp, user agent
- **Self-Protection**: Admins cannot remove themselves from whitelist
- **Role Validation**: Proper permission checks on all endpoints
- **Session Tracking**: Last active timestamps for security monitoring

---

## 🚀 **API Endpoints**

### **Analytics Endpoints**
```
GET /api/admin/analytics/emotions/correlations?days=30
GET /api/admin/analytics/cohorts
GET /api/admin/analytics/anomalies?days=7
GET /api/admin/analytics/predictions
GET /api/admin/analytics/emotion-flow?hours=24
GET /api/admin/analytics/segmentation
```

### **Admin Management Endpoints**
```
GET    /api/admin/whitelist              # List all admin users
POST   /api/admin/whitelist              # Add new admin
PUT    /api/admin/whitelist/:email       # Update admin
DELETE /api/admin/whitelist/:email       # Remove admin
GET    /api/admin/activity-log           # View admin activity
```

---

## 📊 **Dashboard Features**

### **Interactive Charts**
- **Scatter Plots**: Emotion correlations with color-coded significance
- **Bar Charts**: User cohort comparisons with dual y-axes
- **Line Charts**: Trend analysis over time
- **Area Charts**: Emotion flow visualization
- **Tables**: Detailed data with sorting and filtering

### **Real-time Capabilities**
- **Auto-refresh**: 30-second intervals (toggleable)
- **Live Updates**: Real-time emotion flow monitoring
- **Instant Feedback**: Immediate response to user actions

### **Export & Sharing**
- **JSON Export**: Complete analytics data download
- **Timestamp Tracking**: All exports include generation time
- **Filtered Data**: Export respects current time range settings

---

## 🔧 **Configuration & Customization**

### **Time Range Settings**
- **7 days**: Recent activity focus
- **30 days**: Monthly trends
- **90 days**: Quarterly analysis

### **Refresh Intervals**
- **Manual**: On-demand refresh
- **Auto**: 30-second intervals
- **Real-time**: Live emotion flow updates

### **Access Control**
Default admin detection (can be customized):
```typescript
// Current logic in middleware
const isDefaultAdmin = email.toLowerCase().includes('admin') || 
                      email.toLowerCase().includes('rathi');
```

---

## 🎯 **Business Intelligence Insights**

### **Key Metrics to Monitor**
1. **User Retention**: Cohort analysis shows which signup periods have best retention
2. **Emotional Health**: Anomaly detection flags users needing attention
3. **Engagement Patterns**: Flow analysis reveals optimal conversation paths
4. **Churn Prevention**: Predictive analytics enables proactive intervention
5. **Product Optimization**: Correlation analysis shows which emotions drive engagement

### **Actionable Intelligence**
- **High Churn Risk Users**: Automatic identification for re-engagement campaigns
- **Emotional Anomalies**: Early warning system for user wellbeing
- **Optimal Timing**: Peak emotion analysis for feature releases
- **User Segmentation**: Targeted experiences for different user types

---

## 🛡️ **Security & Compliance**

### **Data Protection**
- **Role-based Access**: Granular permissions system
- **Activity Auditing**: Complete admin action logging
- **IP Tracking**: Security monitoring and threat detection
- **Session Management**: Automatic timeout and re-authentication

### **Privacy Considerations**
- **Anonymized Data**: User IDs truncated in displays
- **Aggregated Analytics**: Individual data protected through aggregation
- **Consent Tracking**: Integration with onboarding consent system

---

## 📈 **Performance & Scalability**

### **Optimization Features**
- **Efficient Queries**: Optimized SQL with proper indexing
- **Parallel Processing**: Multiple API calls executed simultaneously
- **Caching Strategy**: Results cached for improved response times
- **Pagination**: Large datasets handled with proper pagination

### **Scalability Considerations**
- **Database Indexing**: Proper indexes on frequently queried columns
- **Query Optimization**: Complex analytics queries optimized for performance
- **Connection Pooling**: Efficient database connection management
- **Background Processing**: Heavy analytics run asynchronously

---

## 🎉 **Getting Started**

### **Initial Setup**
1. **Database**: Ensure PostgreSQL is running with proper schema
2. **Admin User**: Add your email to admin_whitelist table
3. **Environment**: Set proper environment variables
4. **Services**: Start both backend and frontend servers

### **First Login**
1. Navigate to `http://localhost:3001/admin/analytics`
2. Login with your whitelisted admin email
3. Explore the Overview tab to familiarize yourself
4. Add additional admin users through Admin Management tab

### **Daily Operations**
- **Morning Check**: Review overnight anomalies and churn risks
- **Weekly Analysis**: Examine cohort trends and user segmentation
- **Monthly Planning**: Use predictive insights for product roadmap

---

This comprehensive analytics dashboard transforms your ORA platform into a data-driven emotion intelligence system, providing the insights needed to optimize user experience and business outcomes! 🚀
