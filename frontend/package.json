{"name": "ora-hume-frontend", "version": "1.0.0", "description": "Frontend for ORA Hume EVI application", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@google-cloud/speech": "^6.0.0", "@types/recharts": "^2.0.1", "axios": "^1.6.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.12", "hume": "^0.12.2", "lucide-react": "^0.294.0", "ogl": "^1.0.11", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "recharts": "^3.1.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^5.0.0", "vitest": "^3.2.4"}, "engines": {"node": ">=18"}}