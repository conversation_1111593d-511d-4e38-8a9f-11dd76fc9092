import { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { apiService } from '../services/api'

export interface AdminStatus {
  isAdmin: boolean
  isLoading: boolean
  error: string | null
  role?: 'admin' | 'super_admin' | 'viewer'
  permissions?: Record<string, any>
}

export function useAdminAuth(): AdminStatus {
  const { user } = useAuth()
  const [adminStatus, setAdminStatus] = useState<AdminStatus>({
    isAdmin: false,
    isLoading: true,
    error: null
  })

  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!user) {
        setAdminStatus({
          isAdmin: false,
          isLoading: false,
          error: null
        })
        return
      }

      try {
        setAdminStatus(prev => ({ ...prev, isLoading: true, error: null }))
        
        // Try to access admin analytics to check if user is admin
        const response = await apiService.getAdminAnalytics(1)
        
        if (response.success) {
          setAdminStatus({
            isAdmin: true,
            isLoading: false,
            error: null,
            // Note: We don't get role/permissions from this endpoint
            // In a real implementation, you'd have a dedicated endpoint for this
          })
        } else {
          setAdminStatus({
            isAdmin: false,
            isLoading: false,
            error: null
          })
        }
      } catch (error: any) {
        const isUnauthorized = error.response?.status === 403 || error.response?.status === 401
        
        setAdminStatus({
          isAdmin: false,
          isLoading: false,
          error: isUnauthorized ? null : 'Failed to check admin status'
        })
      }
    }

    checkAdminStatus()
  }, [user])

  return adminStatus
}

export function useAdminWhitelist() {
  const [whitelist, setWhitelist] = useState<any[]>([])
  const [stats, setStats] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchWhitelist = async (includeInactive = false) => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await apiService.getAdminWhitelist(includeInactive)
      
      if (response.success) {
        setWhitelist(response.data.admins)
        setStats(response.data.stats)
      } else {
        setError('Failed to fetch admin whitelist')
      }
    } catch (err: any) {
      setError(err.response?.data?.error?.message || 'Failed to fetch admin whitelist')
    } finally {
      setLoading(false)
    }
  }

  const addAdmin = async (adminData: {
    email: string
    role?: string
    permissions?: Record<string, any>
    notes?: string
  }) => {
    try {
      const response = await apiService.addAdminToWhitelist(adminData)
      
      if (response.success) {
        await fetchWhitelist() // Refresh the list
        return { success: true, admin: response.data.admin }
      } else {
        return { success: false, error: 'Failed to add admin' }
      }
    } catch (err: any) {
      return { 
        success: false, 
        error: err.response?.data?.error?.message || 'Failed to add admin' 
      }
    }
  }

  const updateAdmin = async (email: string, updateData: {
    role?: string
    isActive?: boolean
    permissions?: Record<string, any>
    notes?: string
  }) => {
    try {
      const response = await apiService.updateAdminInWhitelist(email, updateData)
      
      if (response.success) {
        await fetchWhitelist() // Refresh the list
        return { success: true, admin: response.data.admin }
      } else {
        return { success: false, error: 'Failed to update admin' }
      }
    } catch (err: any) {
      return { 
        success: false, 
        error: err.response?.data?.error?.message || 'Failed to update admin' 
      }
    }
  }

  const removeAdmin = async (email: string) => {
    try {
      const response = await apiService.removeAdminFromWhitelist(email)
      
      if (response.success) {
        await fetchWhitelist() // Refresh the list
        return { success: true }
      } else {
        return { success: false, error: 'Failed to remove admin' }
      }
    } catch (err: any) {
      return { 
        success: false, 
        error: err.response?.data?.error?.message || 'Failed to remove admin' 
      }
    }
  }

  useEffect(() => {
    fetchWhitelist()
  }, [])

  return {
    whitelist,
    stats,
    loading,
    error,
    fetchWhitelist,
    addAdmin,
    updateAdmin,
    removeAdmin
  }
}

export function useAdminActivityLog() {
  const [activities, setActivities] = useState<any[]>([])
  const [stats, setStats] = useState<any>(null)
  const [pagination, setPagination] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchActivityLog = async (filters: {
    adminEmail?: string
    action?: string
    resource?: string
    startDate?: string
    endDate?: string
    page?: number
    limit?: number
  } = {}) => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await apiService.getAdminActivityLog(filters)
      
      if (response.success) {
        setActivities(response.data.activities)
        setStats(response.data.stats)
        setPagination(response.data.pagination)
      } else {
        setError('Failed to fetch activity log')
      }
    } catch (err: any) {
      setError(err.response?.data?.error?.message || 'Failed to fetch activity log')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchActivityLog()
  }, [])

  return {
    activities,
    stats,
    pagination,
    loading,
    error,
    fetchActivityLog
  }
}
