import { useEffect, useMemo, useState } from 'react'
import { useChat } from '../contexts/ChatContext'
import { useToast } from '../components/ui/Toaster'
import { Mic, PhoneOff } from 'lucide-react'
import VoiceOrb from '../components/voice/VoiceOrb'
import ProfileDropdown from '../components/ui/ProfileDropdown'
import SpaceAmbientElements from '../components/ui/SpaceAmbientElements'

import { useEmotionVisuals } from '../hooks/useEmotionVisuals'
import { generateGridOverlay } from '../utils/emotionColorMapping'
import {
  VoiceInterfaceErrorBoundary,
  usePerformanceMonitor,
  useReducedMotion,
  accessibilityUtils
} from '../utils/performanceOptimization'

export default function ChatPage() {
  const {
    isConnected,
    isConnecting,
    connectionError,
    isChatActive,
    messages,
    isRecording,
    isPlaying,
    connect,
    disconnect: _disconnect,
    startChat,
    endChat
  } = useChat()

  const { addToast } = useToast()

  // Performance and accessibility monitoring
  const performanceMetrics = usePerformanceMonitor()
  const prefersReducedMotion = useReducedMotion()

  // Button state management to prevent multiple clicks
  const [isStartingChat, setIsStartingChat] = useState(false)
  const [isEndingChat, setIsEndingChat] = useState(false)

  // Get latest emotions from messages for real-time visual feedback
  const latestEmotions = useMemo(() => {
    if (messages.length === 0) return {}
    const latestMessage = messages[messages.length - 1]
    return latestMessage?.emotions || {}
  }, [messages])

  // Use the enhanced emotion visuals system with performance optimizations
  const emotionVisuals = useEmotionVisuals(
    latestEmotions,
    isChatActive,
    {
      smoothingFactor: performanceMetrics.shouldReduceEffects ? 0.9 : 0.8,
      transitionDuration: prefersReducedMotion ? 500 : 1500,
      intensityMultiplier: performanceMetrics.shouldReduceEffects ? 0.8 : 1.2
    }
  )

  // Balanced dark background with subtle depth
  const darkBackground = `
    radial-gradient(ellipse at center,
      #1a1a1a 0%,
      #141414 40%,
      #0f0f0f 100%
    )
  `

  useEffect(() => {
    // Auto-connect when component mounts
    if (!isConnected && !isConnecting) {
      handleConnect()
    }

    return () => {
      // Cleanup handled by audio service
    }
  }, [])

  const handleConnect = async () => {
    try {
      await connect()
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Connection Failed',
        message: 'Failed to connect to chat server'
      })
    }
  }

  const handleStartChat = async () => {
    // Prevent multiple simultaneous clicks
    if (isStartingChat || isChatActive) {
      return
    }

    setIsStartingChat(true)
    try {
      // Start chat (this will handle microphone access and audio setup)
      await startChat()
      accessibilityUtils.announceVoiceState('listening')
    } catch (error) {
      console.error('Failed to start chat:', error)
      addToast({
        type: 'error',
        title: 'Microphone Access Required',
        message: 'Please allow microphone access to start voice chat'
      })
      accessibilityUtils.announceToScreenReader('Failed to start voice chat. Please check microphone permissions.')
    } finally {
      setIsStartingChat(false)
    }
  }

  const handleEndChat = () => {
    // Prevent multiple simultaneous clicks
    if (isEndingChat || !isChatActive) {
      return
    }

    setIsEndingChat(true)
    try {
      endChat()
      accessibilityUtils.announceVoiceState('idle')
    } finally {
      // Reset the button state after a short delay to ensure UI updates
      setTimeout(() => setIsEndingChat(false), 500)
    }
  }

  // Announce voice state changes for accessibility
  useEffect(() => {
    if (isRecording) {
      accessibilityUtils.announceVoiceState('listening')
    } else if (isPlaying) {
      accessibilityUtils.announceVoiceState('speaking')
    } else if (isChatActive && !isRecording && !isPlaying) {
      accessibilityUtils.announceVoiceState('processing')
    }
  }, [isRecording, isPlaying, isChatActive])

  return (
    <VoiceInterfaceErrorBoundary>
      <div
        className="h-screen w-full relative overflow-hidden transition-all duration-1000 ease-out"
        style={{
          background: darkBackground,
          backgroundImage: generateGridOverlay(),
          backgroundSize: '40px 40px'
        }}
        role="main"
        aria-label="Voice chat interface"
      >
        {/* Subtle vignette effect */}
        <div className="absolute inset-0 bg-gradient-radial from-transparent via-transparent to-gray-900/10 pointer-events-none" />

        {/* Space Ambient Elements */}
        <SpaceAmbientElements
          intensity={emotionVisuals.intensity > 0.7 ? 'high' : emotionVisuals.intensity > 0.4 ? 'medium' : 'low'}
          isActive={true}
        />

        {/* Profile Dropdown - Top Right */}
        <div className="absolute top-6 right-6 z-20">
          <ProfileDropdown />
        </div>

        {/* Main Content */}
        <div className="relative z-10 h-full flex flex-col items-center justify-center">
          {/* Voice Orb */}
          <div className="mb-12">
            <VoiceOrb
              emotions={emotionVisuals.currentEmotions}
              isListening={isRecording}
              isProcessing={isChatActive && !isRecording && !isPlaying}
              isSpeaking={isPlaying}
              isIdle={!isChatActive}
              intensity={emotionVisuals.intensity}
              size="large"
            />
          </div>

          {/* Voice State Indicator */}
          <div className="mb-8 text-center">
            {!isConnected ? (
              <div className="text-slate-300/90">
                <div className="animate-pulse mb-2 text-lg">Connecting to ORA...</div>
              </div>
            ) : !isChatActive ? (
              <div className="text-slate-200">
                <div className="text-xl font-medium mb-3 text-white">Ready to chat with ORA</div>
                <div className="text-base text-slate-300">Tap the orb to begin your voice conversation</div>
              </div>
            ) : isRecording ? (
              <div className="text-blue-300">
                <div className="flex items-center justify-center space-x-3 mb-3">
                  <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse shadow-lg shadow-blue-400/50"></div>
                  <span className="text-xl font-medium">Listening...</span>
                </div>
                <div className="text-base text-slate-300">Speak naturally, ORA is listening</div>
              </div>
            ) : isPlaying ? (
              <div className="text-emerald-300">
                <div className="flex items-center justify-center space-x-3 mb-3">
                  <div className="w-3 h-3 bg-emerald-400 rounded-full animate-pulse shadow-lg shadow-emerald-400/50"></div>
                  <span className="text-xl font-medium">ORA is speaking...</span>
                </div>
                <div className="text-base text-slate-300">Listen to ORA's response</div>
              </div>
            ) : (
              <div className="text-purple-300">
                <div className="text-xl font-medium mb-3">Processing...</div>
                <div className="text-base text-slate-300">ORA is thinking about your message</div>
              </div>
            )}
          </div>

          {/* Voice Controls */}
          <div className="flex items-center justify-center space-x-6">
            {!isChatActive ? (
              <button
                onClick={handleStartChat}
                onKeyDown={(e) => accessibilityUtils.handleKeyboardNavigation(e, handleStartChat)}
                disabled={!isConnected || isStartingChat}
                aria-label="Start voice conversation with ORA"
                aria-describedby="voice-status"
                className={`
                  w-20 h-20 rounded-full flex items-center justify-center
                  transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-white/20
                  ${(isConnected && !isStartingChat)
                    ? 'bg-gradient-to-r from-gray-800 to-gray-900 text-white shadow-lg shadow-white/10 hover:shadow-xl hover:shadow-white/20 border border-white/20'
                    : 'bg-gray-900 text-gray-500 cursor-not-allowed border border-gray-700'
                  }
                `}
              >
                <Mic className="w-8 h-8" />
              </button>
            ) : (
              <button
                onClick={handleEndChat}
                onKeyDown={(e) => accessibilityUtils.handleKeyboardNavigation(e, handleEndChat)}
                disabled={isEndingChat}
                aria-label="End voice conversation"
                className={`
                  w-16 h-16 rounded-full flex items-center justify-center
                  bg-gradient-to-r from-gray-800 to-gray-900 text-white border border-white/20
                  transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-white/20
                  shadow-lg shadow-white/10 hover:shadow-xl hover:shadow-white/20
                  ${isEndingChat ? 'opacity-50 cursor-not-allowed' : ''}
                `}
              >
                <PhoneOff className="w-6 h-6" />
              </button>
            )}
          </div>

          {/* Connection Error */}
          {connectionError && (
            <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
              <div className="bg-black/90 border border-white/20 rounded-lg p-4 shadow-lg backdrop-blur-sm">
                <p className="text-sm text-white">{connectionError}</p>
              </div>
            </div>
          )}

          {/* Subtle conversation count indicator */}
          {messages.length > 0 && (
            <div className="absolute top-20 right-6">
              <div className="bg-black/60 backdrop-blur-sm rounded-full px-3 py-1 text-xs text-white border border-white/20">
                {messages.length} exchange{messages.length !== 1 ? 's' : ''}
              </div>
            </div>
          )}

          {/* Performance indicator for development */}
          {process.env.NODE_ENV === 'development' && (
            <div className="absolute top-8 left-8">
              <div className={`
                bg-black/60 backdrop-blur-sm rounded-full px-3 py-1 text-xs border border-white/20
                ${performanceMetrics.isOptimal ? 'text-green-400' : 'text-red-400'}
              `}>
                {performanceMetrics.fps} FPS
              </div>
            </div>
          )}

          {/* Screen reader status updates */}
          <div id="voice-status" className="sr-only" aria-live="polite" aria-atomic="true">
            {!isConnected ? 'Connecting to ORA...' :
             !isChatActive ? 'Voice chat ready. Press Enter or Space to start.' :
             isRecording ? 'ORA is listening. Speak now.' :
             isPlaying ? 'ORA is responding.' :
             'ORA is processing your message.'}
          </div>
        </div>
      </div>
    </VoiceInterfaceErrorBoundary>
  )
}
