import { useEffect } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { LoadingSpinner } from '../components/ui/LoadingSpinner'

export default function AuthCallbackPage() {
  const navigate = useNavigate()
  const { login: _login } = useAuth()
  const [searchParams] = useSearchParams()

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const token = searchParams.get('token')
        const user = searchParams.get('user')
        
        if (token && user) {
          // For URL-based callback (not recommended for production)
          localStorage.setItem('accessToken', token)
          const refreshToken = searchParams.get('refresh_token')
          if (refreshToken) {
            localStorage.setItem('refreshToken', refreshToken)
          }
          
          navigate('/chat')
        } else {
          throw new Error('Missing authentication data')
        }
      } catch (error) {
        console.error('Auth callback error:', error)
        navigate('/auth/error?error=callback_failed')
      }
    }

    handleCallback()
  }, [searchParams, navigate])

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <LoadingSpinner size="lg" />
        <p className="mt-4 text-gray-600">Completing authentication...</p>
      </div>
    </div>
  )
}
