import React, { useState, useEffect } from 'react'
import { Orb } from '../components/ui/shadcn-io/orb'

export default function OrbTestPage() {
  const [hue, setHue] = useState(180)
  const [voiceState, setVoiceState] = useState<'idle' | 'listening'>('idle')
  const [hoverIntensity, setHoverIntensity] = useState(0.3)
  const [emotionIntensity, setEmotionIntensity] = useState(0.3)
  const [autoDemo, setAutoDemo] = useState(false)

  // Auto demo cycle through states
  useEffect(() => {
    if (!autoDemo) return

    const interval = setInterval(() => {
      const states: Array<'idle' | 'listening'> = ['idle', 'listening']
      const currentIndex = states.indexOf(voiceState)
      const nextIndex = (currentIndex + 1) % states.length
      setVoiceState(states[nextIndex])

      // Randomize other values for demo
      setHue(Math.random() * 360)
      setHoverIntensity(Math.random() * 0.5) // Cap at 50% as requested
      setEmotionIntensity(Math.random())
    }, 3000)

    return () => clearInterval(interval)
  }, [autoDemo, voiceState])

  return (
    <div className="min-h-screen bg-black text-white p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">Enhanced Orb Test Page</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Orb Display */}
          <div className="flex flex-col items-center">
            <div className="w-[32rem] h-[32rem] bg-gray-900 rounded-lg flex items-center justify-center mb-4">
              <div className="w-[30rem] h-[30rem]">
                <Orb
                  hue={hue}
                  hoverIntensity={hoverIntensity}
                  rotateOnHover={true}
                  forceHoverState={voiceState === 'listening'}
                  voiceState={voiceState}
                  emotionIntensity={emotionIntensity}
                />
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-xl font-semibold mb-2">Current State: {voiceState}</div>
              <div className="text-sm text-gray-400">
                Hue: {Math.round(hue)}° | Hover: {hoverIntensity.toFixed(2)} | Emotion: {emotionIntensity.toFixed(2)}
              </div>
            </div>
          </div>

          {/* Controls */}
          <div className="space-y-6">
            <div>
              <h2 className="text-xl font-semibold mb-4">Voice State Controls</h2>
              <div className="grid grid-cols-2 gap-2">
                {(['idle', 'listening'] as const).map((state) => (
                  <button
                    key={state}
                    onClick={() => setVoiceState(state)}
                    className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                      voiceState === state
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    }`}
                  >
                    {state.charAt(0).toUpperCase() + state.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <h2 className="text-xl font-semibold mb-4">Hue Control</h2>
              <div className="space-y-2">
                <input
                  type="range"
                  min="0"
                  max="360"
                  value={hue}
                  onChange={(e) => setHue(Number(e.target.value))}
                  className="w-full"
                />
                <div className="text-sm text-gray-400">Hue: {Math.round(hue)}°</div>
              </div>
            </div>

            <div>
              <h2 className="text-xl font-semibold mb-4">Hover Intensity</h2>
              <div className="space-y-2">
                <input
                  type="range"
                  min="0"
                  max="0.5"
                  step="0.01"
                  value={hoverIntensity}
                  onChange={(e) => setHoverIntensity(Number(e.target.value))}
                  className="w-full"
                />
                <div className="text-sm text-gray-400">Intensity: {hoverIntensity.toFixed(2)} (Max: 0.5)</div>
              </div>
            </div>

            <div>
              <h2 className="text-xl font-semibold mb-4">Emotion Intensity</h2>
              <div className="space-y-2">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  value={emotionIntensity}
                  onChange={(e) => setEmotionIntensity(Number(e.target.value))}
                  className="w-full"
                />
                <div className="text-sm text-gray-400">Intensity: {emotionIntensity.toFixed(2)}</div>
              </div>
            </div>

            <div>
              <h2 className="text-xl font-semibold mb-4">Auto Demo</h2>
              <button
                onClick={() => setAutoDemo(!autoDemo)}
                className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                  autoDemo
                    ? 'bg-red-600 text-white hover:bg-red-700'
                    : 'bg-green-600 text-white hover:bg-green-700'
                }`}
              >
                {autoDemo ? 'Stop Auto Demo' : 'Start Auto Demo'}
              </button>
            </div>

            <div className="bg-gray-800 p-4 rounded-lg">
              <h3 className="font-semibold mb-2">Voice State Descriptions:</h3>
              <ul className="text-sm space-y-1 text-gray-300">
                <li><strong>Idle:</strong> Default calm state with gentle animation and low hover intensity</li>
                <li><strong>Listening:</strong> Active breathing-like motion with enhanced hover effects</li>
              </ul>
              <div className="mt-3">
                <h4 className="font-semibold mb-1 text-sm">Improvements Made:</h4>
                <ul className="text-xs space-y-1 text-gray-400">
                  <li>• Reduced jitter by capping hover intensity at 40%</li>
                  <li>• Simplified to 2 states: Idle and Listening</li>
                  <li>• Made orb larger and thicker (reduced innerRadius)</li>
                  <li>• Smooth transitions between states</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-8 text-center">
          <a
            href="/chat2"
            className="inline-block px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go to Chat2 (Live Implementation)
          </a>
        </div>
      </div>
    </div>
  )
}
