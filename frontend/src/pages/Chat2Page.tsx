import React, { useEffect, useMemo, useState, useRef, useCallback } from 'react'
import { useChat } from '../contexts/ChatContext'
import { useToast } from '../components/ui/Toaster'
import { Mic, PhoneOff } from 'lucide-react'
import ProfileDropdown from '../components/ui/ProfileDropdown'
import { Orb } from '../components/ui/shadcn-io/orb'

import { blendEmotionHues, getEmotionIntensity, getDominantEmotion } from '../utils/emotionToHue'
import {
  VoiceInterfaceErrorBoundary,
  accessibilityUtils
} from '../utils/performanceOptimization'

/**
 * Custom hook for emotion persistence and accumulation - DISABLED for immediate emotion changes
 */
function useEmotionPersistence(emotions: Record<string, number>) {
  // DISABLED: Return emotions directly without persistence for immediate changes
  // This ensures dominant emotion changes immediately after each utterance
  return emotions
}

/**
 * Custom hook for smooth emotion transitions to prevent orb flickering
 */
function useSmoothEmotionTransitions(emotions: Record<string, number>) {
  const [smoothedEmotions, setSmoothedEmotions] = useState<Record<string, number>>({})
  const [smoothedHue, setSmoothedHue] = useState<number>(180)
  const [smoothedIntensity, setSmoothedIntensity] = useState<number>(0.6)
  const [smoothedDominant, setSmoothedDominant] = useState<string>('default')

  const animationFrameRef = useRef<number>()
  const lastUpdateRef = useRef<number>(0)

  // Emotion persistence disabled for immediate emotion changes

  // Smoothing parameters - More responsive for immediate emotion changes
  const SMOOTHING_FACTOR = 0.35 // Much faster for immediate emotion changes
  const UPDATE_INTERVAL = 30 // Update every 30ms for faster transitions
  const DEBOUNCE_DELAY = 50 // Minimal debounce for immediate responsiveness

  const smoothValue = useCallback((current: number, target: number, factor: number): number => {
    return current + (target - current) * factor
  }, [])

  const smoothHue = useCallback((current: number, target: number, factor: number): number => {
    // Handle hue wrapping (0-360 degrees)
    let diff = target - current
    if (diff > 180) diff -= 360
    if (diff < -180) diff += 360

    let result = current + diff * factor
    if (result < 0) result += 360
    if (result >= 360) result -= 360

    return result
  }, [])

  useEffect(() => {
    const now = Date.now()

    // Debounce rapid emotion changes
    if (now - lastUpdateRef.current < DEBOUNCE_DELAY) {
      return
    }

    const animate = () => {
      const currentTime = Date.now()
      if (currentTime - lastUpdateRef.current < UPDATE_INTERVAL) {
        animationFrameRef.current = requestAnimationFrame(animate)
        return
      }

      lastUpdateRef.current = currentTime

      // Use fresh emotions directly for immediate changes (no persistence)
      const emotionsToUse = emotions

      // Debug emotion processing
      if (Object.keys(emotionsToUse).length > 0) {
        console.log(`🔄 SMOOTHING: Processing emotions:`, Object.keys(emotionsToUse))
        console.log(`🔄 SMOOTHING: Top 3 emotions:`, Object.entries(emotionsToUse)
          .sort(([,a], [,b]) => b - a)
          .slice(0, 3)
          .map(([emotion, score]) => `${emotion}: ${score.toFixed(3)}`)
          .join(', '))
      }

      // Calculate target values
      const targetHue = blendEmotionHues(emotionsToUse)
      const targetIntensity = getEmotionIntensity(emotionsToUse)
      const targetDominant = getDominantEmotion(emotionsToUse)

      console.log(`🔄 SMOOTHING: Targets - Hue: ${targetHue}°, Intensity: ${targetIntensity.toFixed(2)}, Dominant: ${targetDominant}`)

      setSmoothedHue(prev => smoothHue(prev, targetHue, SMOOTHING_FACTOR))
      setSmoothedIntensity(prev => smoothValue(prev, targetIntensity, SMOOTHING_FACTOR))

      // Update dominant emotion IMMEDIATELY when new emotions arrive
      if (targetDominant !== smoothedDominant && Object.keys(emotionsToUse).length > 0) {
        console.log(`🎯 Dominant emotion changing from ${smoothedDominant} to ${targetDominant}`)
        setSmoothedDominant(targetDominant)
      }

      // Smooth emotion values
      setSmoothedEmotions(prev => {
        const newEmotions: Record<string, number> = {}

        // Smooth existing emotions
        for (const [emotion, currentValue] of Object.entries(prev)) {
          const targetValue = emotionsToUse[emotion] || 0
          newEmotions[emotion] = smoothValue(currentValue, targetValue, SMOOTHING_FACTOR)
        }

        // Add new emotions gradually
        for (const [emotion, targetValue] of Object.entries(emotionsToUse)) {
          if (!(emotion in prev)) {
            newEmotions[emotion] = targetValue * SMOOTHING_FACTOR
          }
        }

        return newEmotions
      })

      // Check if we need to continue animating
      const hueDistance = Math.abs(targetHue - smoothedHue)
      const intensityDistance = Math.abs(targetIntensity - smoothedIntensity)

      if (hueDistance > 1 || intensityDistance > 0.01) {
        animationFrameRef.current = requestAnimationFrame(animate)
      }
    }

    // Start animation
    animationFrameRef.current = requestAnimationFrame(animate)

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [emotions, smoothedHue, smoothedIntensity, smoothedDominant, smoothValue, smoothHue])

  return {
    emotions: smoothedEmotions,
    hue: Math.round(smoothedHue),
    intensity: smoothedIntensity,
    dominantEmotion: smoothedDominant
  }
}

/**
 * Memoized Orb component to prevent unnecessary re-renders
 */
const MemoizedOrb = React.memo(Orb, (prevProps, nextProps) => {
  // Only re-render if significant changes occur
  const hueChanged = Math.abs((prevProps.hue || 0) - (nextProps.hue || 0)) > 2
  const intensityChanged = Math.abs((prevProps.hoverIntensity || 0) - (nextProps.hoverIntensity || 0)) > 0.05
  const stateChanged = prevProps.forceHoverState !== nextProps.forceHoverState
  const voiceStateChanged = prevProps.voiceState !== nextProps.voiceState
  const emotionIntensityChanged = Math.abs((prevProps.emotionIntensity || 0) - (nextProps.emotionIntensity || 0)) > 0.05

  return !hueChanged && !intensityChanged && !stateChanged && !voiceStateChanged && !emotionIntensityChanged
})

export default function Chat2Page() {
  const {
    isConnected,
    isConnecting,
    connectionError,
    isChatActive,
    messages,
    isRecording,
    isPlaying,
    connect,
    disconnect: _disconnect,
    startChat,
    endChat
  } = useChat()

  const { addToast } = useToast()

  // Button state management to prevent multiple clicks
  const [isStartingChat, setIsStartingChat] = useState(false)
  const [isEndingChat, setIsEndingChat] = useState(false)

  // Get latest emotions from messages for real-time visual feedback
  const latestEmotions = useMemo(() => {
    if (messages.length === 0) {
      return {}
    }

    // Get the most recent message with emotions (both user AND assistant messages)
    const messagesWithEmotions = messages.filter(m => m.emotions && Object.keys(m.emotions).length > 0)
    console.log(`🎭 Found ${messagesWithEmotions.length} messages with emotions out of ${messages.length} total messages`)

    const latestMessage = messagesWithEmotions[messagesWithEmotions.length - 1]

    if (latestMessage) {
      const roleIcon = latestMessage.role === 'user' ? '👤' : '🤖'
      console.log(`🎭 ${roleIcon} Using emotions from latest ${latestMessage.role} message:`, Object.keys(latestMessage.emotions || {}))
      console.log(`🎭 ${roleIcon} Top emotions:`, Object.entries(latestMessage.emotions || {})
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([emotion, score]) => `${emotion}: ${score.toFixed(3)}`)
        .join(', '))
      return latestMessage.emotions
    }

    console.log(`🎭 No messages with emotions found`)
    return {}
  }, [messages])

  // Use smooth emotion transitions to prevent flickering
  const smoothEmotions = useSmoothEmotionTransitions(latestEmotions || {})

  // Extract smoothed values
  const orbHue = smoothEmotions.hue
  const orbIntensity = smoothEmotions.intensity
  const dominantEmotion = smoothEmotions.dominantEmotion

  // Debug dominant emotion changes and color updates
  const prevDominantRef = useRef<string>('default')
  const prevHueRef = useRef<number>(180)
  const prevIntensityRef = useRef<number>(0.3)

  useEffect(() => {
    const hueChanged = Math.abs(orbHue - prevHueRef.current) > 5
    const intensityChanged = Math.abs(orbIntensity - prevIntensityRef.current) > 0.05
    const emotionChanged = dominantEmotion !== prevDominantRef.current

    if (emotionChanged || hueChanged || intensityChanged) {
      prevDominantRef.current = dominantEmotion
      prevHueRef.current = orbHue
      prevIntensityRef.current = orbIntensity
    }
  }, [dominantEmotion, orbHue, orbIntensity, latestEmotions])

  // Determine voice state for orb animation - simplified to idle and listening
  const getVoiceState = (): 'idle' | 'listening' => {
    if (!isChatActive) return 'idle'
    if (isRecording) return 'listening'
    // For speaking and processing, we'll use hover intensity instead
    return 'idle'
  }

  // Calculate hover intensity based on chat state with auto-hover baseline
  // Following orb-test implementation with capped intensity to reduce jitter
  const getHoverIntensity = (): number => {
    const baseAutoHover = 0.1; // Continuous auto-hover as requested
    const maxIntensity = 0.5; // Cap at 50% like orb-test to reduce jitter

    let targetIntensity: number;

    if (!isChatActive) {
      targetIntensity = Math.max(baseAutoHover, orbIntensity * 0.3) // Low intensity for idle
    } else if (isRecording) {
      targetIntensity = 0.4 // Nice hover intensity for listening
    } else if (isPlaying) {
      targetIntensity = 0.3 // Moderate intensity for speaking
    } else {
      targetIntensity = 0.25 // Processing state
    }

    // Cap the intensity to prevent jitter, following orb-test approach
    return Math.min(maxIntensity, Math.max(baseAutoHover, targetIntensity))
  }

  // Auto-connect when component mounts
  useEffect(() => {
    if (!isConnected && !isConnecting) {
      connect()
    }
  }, [isConnected, isConnecting, connect])

  // Handle connection errors
  useEffect(() => {
    if (connectionError) {
      addToast({
        type: 'error',
        title: 'Connection Error',
        message: connectionError
      })
    }
  }, [connectionError, addToast])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.code === 'Space' || e.code === 'Enter') {
        e.preventDefault()
        if (!isChatActive && isConnected && !isStartingChat) {
          handleStartChat()
        } else if (isChatActive && !isEndingChat) {
          handleEndChat()
        }
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [isChatActive, isConnected, isStartingChat, isEndingChat])

  const handleConnect = async () => {
    try {
      await connect()
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Connection Failed',
        message: 'Failed to connect to chat server'
      })
    }
  }

  const handleStartChat = async () => {
    // Prevent multiple simultaneous clicks
    if (isStartingChat || isChatActive) {
      return
    }

    setIsStartingChat(true)
    try {
      // Start chat (this will handle microphone access and audio setup)
      await startChat()
      accessibilityUtils.announceVoiceState('listening')
    } catch (error) {
      console.error('Failed to start chat:', error)
      addToast({
        type: 'error',
        title: 'Microphone Access Required',
        message: 'Please allow microphone access to start voice chat'
      })
      accessibilityUtils.announceToScreenReader('Failed to start voice chat. Please check microphone permissions.')
    } finally {
      setIsStartingChat(false)
    }
  }

  const handleEndChat = () => {
    // Prevent multiple simultaneous clicks
    if (isEndingChat || !isChatActive) {
      return
    }

    setIsEndingChat(true)
    try {
      endChat()
      accessibilityUtils.announceVoiceState('idle')
    } finally {
      // Reset the button state after a short delay to ensure UI updates
      setTimeout(() => setIsEndingChat(false), 500)
    }
  }

  return (
    <VoiceInterfaceErrorBoundary>
      <div
        className="h-screen w-full relative overflow-hidden bg-black"
        role="main"
        aria-label="Voice chat interface with orb background"
      >
        {/* Shadcn.io Orb Background - Perfectly centered and optimized */}
        <div className="absolute inset-0 z-10 flex items-center justify-center">
          <div className="w-[45rem] h-[45rem] flex items-center justify-center transition-all duration-1000 ease-out"> {/* Increased size with smooth transitions */}
            <MemoizedOrb
              hue={orbHue}
              hoverIntensity={getHoverIntensity()}
              rotateOnHover={true}
              forceHoverState={true} // Always keep some hover state for continuous animation
              voiceState={getVoiceState()}
              emotionIntensity={orbIntensity}
            />
          </div>
        </div>

        {/* Subtle vignette effect for depth */}
        <div className="absolute inset-0 bg-gradient-radial from-transparent via-transparent to-black/30 pointer-events-none z-5" />

        {/* Profile Dropdown - Top Right */}
        <div className="absolute top-6 right-6 z-30">
          <ProfileDropdown />
        </div>

        {/* Main Content */}
        <div className="relative z-20 h-full flex flex-col items-center justify-end pb-24"> {/* Changed to justify-end and added padding-bottom */}

          {/* Voice State Indicator - Elegant and minimal */}
          <div className="mb-12 text-center">
            {!isConnected ? (
              <div className="text-white/90">
                <div className="animate-pulse text-lg font-light">Connecting to ORA...</div>
              </div>
            ) : !isChatActive ? (
              <div className="text-white">
                <div className="text-2xl font-light mb-2">Ready to chat with ORA</div>
                <div className="text-base text-white/70 font-light">Press the button below to begin</div>
              </div>
            ) : isRecording ? (
              <div className="text-white">
                <div className="flex items-center justify-center space-x-3 mb-2">
                  <div className="w-2 h-2 bg-white rounded-full animate-pulse shadow-lg shadow-white/50"></div>
                  <span className="text-xl font-light">Listening</span>
                </div>
                <div className="text-sm text-white/60 font-light">Speak naturally</div>
              </div>
            ) : isPlaying ? (
              <div className="text-white">
                <div className="flex items-center justify-center space-x-3 mb-2">
                  <div className="w-2 h-2 bg-white rounded-full animate-pulse shadow-lg shadow-white/50"></div>
                  <span className="text-xl font-light">ORA is speaking</span>
                </div>
                <div className="text-sm text-white/60 font-light">Listen carefully</div>
              </div>
            ) : (
              <div className="text-white">
                <div className="flex items-center justify-center space-x-3 mb-2">
                  <div className="w-2 h-2 bg-white rounded-full animate-pulse shadow-lg shadow-white/50"></div>
                  <span className="text-xl font-light">Processing</span>
                </div>
                <div className="text-sm text-white/60 font-light">ORA is thinking</div>
              </div>
            )}
          </div>

          {/* Voice Controls */}
          <div className="flex items-center justify-center space-x-6">
            {!isChatActive ? (
              <button
                onClick={handleStartChat}
                onKeyDown={(e) => accessibilityUtils.handleKeyboardNavigation(e, handleStartChat)}
                disabled={!isConnected || isStartingChat}
                aria-label="Start voice conversation with ORA"
                aria-describedby="voice-status"
                className={`
                  w-20 h-20 rounded-full flex items-center justify-center
                  transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-white/20
                  ${(isConnected && !isStartingChat)
                    ? 'bg-black/80 backdrop-blur-sm text-white shadow-lg shadow-black/30 hover:shadow-xl hover:shadow-black/40 border border-white/30'
                    : 'bg-gray-900/50 text-gray-500 cursor-not-allowed border border-gray-700'
                  }
                `}
              >
                <Mic className="w-8 h-8" />
              </button>
            ) : (
              <button
                onClick={handleEndChat}
                onKeyDown={(e) => accessibilityUtils.handleKeyboardNavigation(e, handleEndChat)}
                disabled={isEndingChat}
                aria-label="End voice conversation"
                className={`
                  w-20 h-20 rounded-full flex items-center justify-center
                  bg-black/80 backdrop-blur-sm text-white shadow-lg shadow-black/30
                  transition-all duration-300 transform hover:scale-105 hover:bg-black/90
                  focus:outline-none focus:ring-4 focus:ring-white/20 border border-white/30
                  ${isEndingChat ? 'opacity-50 cursor-not-allowed' : ''}
                `}
              >
                <PhoneOff className="w-8 h-8" />
              </button>
            )}
          </div>

          {/* Connection Status */}
          {!isConnected && (
            <div className="mt-8 text-center">
              <button
                onClick={handleConnect}
                className="px-6 py-3 bg-white/10 backdrop-blur-sm text-white rounded-lg border border-white/20 hover:bg-white/20 transition-all duration-200"
              >
                Reconnect
              </button>
            </div>
          )}



          {/* Screen reader status updates */}
          <div id="voice-status" className="sr-only" aria-live="polite" aria-atomic="true">
            {!isConnected ? 'Connecting to ORA...' :
             !isChatActive ? 'Voice chat ready. Press Enter or Space to start.' :
             isRecording ? 'ORA is listening. Speak now.' :
             isPlaying ? 'ORA is responding.' :
             'ORA is processing your message.'}
          </div>
        </div>
      </div>
    </VoiceInterfaceErrorBoundary>
  )
}
