/**
 * Maps Hume emotions to hue values (0-360) for the shadcn Orb component
 * Based on color psychology and emotional associations
 */

export interface EmotionHueMapping {
  [emotion: string]: number;
}

/**
 * Complete mapping of all 48 Hume emotions to hue values
 * Psychologically optimized: Negative emotions in calming pink/red, Positive in happy green/warm, Neutral in cool blues
 * Hue values are in degrees (0-360) for HSL color space
 */
export const EMOTION_HUE_MAPPING: EmotionHueMapping = {
  // POSITIVE EMOTIONS - Happy greens, warm yellows, and oranges (60-150°)
  joy: 120,              // Bright green - happiness
  amusement: 110,        // Yellow-green - playful
  ecstasy: 130,          // Green - pure joy
  excitement: 100,       // Lime green - energetic
  triumph: 90,           // Yellow-green - victory
  admiration: 80,        // Yellow - respect
  aesthetic_appreciation: 85, // Golden yellow - beauty
  love: 140,             // Emerald green - deep love
  adoration: 135,        // Green - affection
  romance: 145,          // Forest green - romantic
  satisfaction: 115,     // Fresh green - contentment
  relief: 125,           // Soothing green - peace
  pride: 95,             // Golden - achievement
  determination: 105,    // Strong green - focus

  // NEUTRAL/CONTEMPLATIVE EMOTIONS - Cool blues and purples (180-270°)
  calmness: 210,         // Sky blue - serenity
  contemplation: 240,    // Deep blue - thought
  concentration: 230,    // Blue - focus
  realization: 250,      // Purple-blue - insight
  
  // NEUTRAL/CONTEMPLATIVE EMOTIONS CONTINUED - Cool blues and purples (160-270°)
  surprise: 170,         // Cyan - unexpected
  awe: 180,              // Light blue - wonder
  interest: 160,         // Teal - curiosity
  curiosity: 165,        // Blue-teal - exploration
  confusion: 260,        // Purple - uncertainty
  doubt: 270,            // Deep purple - questioning
  boredom: 200,          // Neutral blue - disengagement
  nostalgia: 280,        // Purple - bittersweet
  yearning: 290,         // Deep purple - longing

  // NEGATIVE EMOTIONS - Calming pink/red hues (300-360°, 0-30°)
  // These are intentionally in warm, calming tones to be soothing rather than jarring
  sadness: 315,          // Magenta-pink - gentle sadness
  grief: 310,            // Deep magenta - profound but soft
  disappointment: 320,   // Pink - soft disappointment
  fear: 330,             // Rose pink - gentle fear
  anxiety: 325,          // Warm pink - soothing anxiety
  horror: 335,           // Deep rose - contained horror
  nervousness: 340,      // Light pink - mild nervousness
  anger: 350,            // Soft red-pink - controlled anger
  annoyance: 355,        // Light red-pink - mild irritation
  rage: 345,             // Pink-red - intense but calming
  irritation: 5,         // Warm red - gentle irritation
  disgust: 300,          // Magenta - soft aversion
  distress: 305,         // Pink-magenta - comforting distress
  shame: 20,             // Coral - gentle shame
  guilt: 15,             // Warm coral - soft guilt
  embarrassment: 25,     // Light coral - mild embarrassment
  empathic_pain: 335,    // Rose - compassionate pain

  // MIXED/COMPLEX EMOTIONS - Transitional hues
  envy: 150,             // Yellow-green - complex emotion
  sympathy: 345,         // Pink-red - caring
  craving: 30,           // Orange-red - desire

  // Default fallback - neutral blue
  default: 200
};

/**
 * Get hue value for a specific emotion
 */
export function getEmotionHue(emotion: string): number {
  const normalizedEmotion = emotion.toLowerCase().replace(/[^a-z]/g, '');
  return EMOTION_HUE_MAPPING[normalizedEmotion] || EMOTION_HUE_MAPPING.default;
}

/**
 * Normalize emotion intensities for better visual representation
 * Real-world Hume emotions are typically low (0.1-0.3), so we need to amplify them
 */
function normalizeEmotionIntensities(emotions: Record<string, number>): Record<string, number> {
  if (!emotions || Object.keys(emotions).length === 0) {
    return {};
  }

  const entries = Object.entries(emotions);
  const maxIntensity = Math.max(...entries.map(([, intensity]) => intensity));

  // If max intensity is very low, apply amplification
  const amplificationFactor = maxIntensity < 0.3 ? 2.5 : 1.5;

  const normalized: Record<string, number> = {};
  for (const [emotion, intensity] of entries) {
    // Apply amplification and cap at 1.0
    normalized[emotion] = Math.min(1.0, intensity * amplificationFactor);
  }

  return normalized;
}

/**
 * Calculate blended hue from multiple emotions based on their intensities
 */
export function blendEmotionHues(emotions: Record<string, number>): number {
  if (!emotions || Object.keys(emotions).length === 0) {
    console.log(`🎨 blendEmotionHues: No emotions, using default hue ${EMOTION_HUE_MAPPING.default}°`);
    return EMOTION_HUE_MAPPING.default;
  }

  console.log(`🎨 blendEmotionHues: Input emotions:`, Object.entries(emotions)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5)
    .map(([emotion, score]) => `${emotion}: ${score.toFixed(3)}`)
    .join(', '));

  // Normalize emotions for better visual representation
  const normalizedEmotions = normalizeEmotionIntensities(emotions);
  console.log(`🎨 blendEmotionHues: After normalization:`, Object.entries(normalizedEmotions)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 3)
    .map(([emotion, score]) => `${emotion}: ${score.toFixed(3)}`)
    .join(', '));

  // Filter out emotions with very low intensity (lowered threshold for real-world data)
  const significantEmotions = Object.entries(normalizedEmotions)
    .filter(([, intensity]) => intensity > 0.05) // Lowered from 0.1 to 0.05
    .sort(([, a], [, b]) => b - a); // Sort by intensity descending

  console.log(`🎨 blendEmotionHues: Significant emotions (>${0.05}):`, significantEmotions.length,
    significantEmotions.slice(0, 3).map(([emotion, score]) => `${emotion}: ${score.toFixed(3)}`).join(', '));

  if (significantEmotions.length === 0) {
    console.log(`🎨 blendEmotionHues: No significant emotions, using default hue ${EMOTION_HUE_MAPPING.default}°`);
    return EMOTION_HUE_MAPPING.default;
  }

  // If only one significant emotion, use its hue
  if (significantEmotions.length === 1) {
    const [emotion] = significantEmotions[0];
    const hue = getEmotionHue(emotion);
    console.log(`🎨 blendEmotionHues: Single emotion ${emotion} → hue ${hue}°`);
    return hue;
  }

  // Blend the top 3 emotions weighted by their intensities
  const topEmotions = significantEmotions.slice(0, 3);
  let totalWeight = 0;
  let weightedHueSum = 0;

  console.log(`🎨 blendEmotionHues: Blending top emotions:`, topEmotions.map(([emotion, intensity]) =>
    `${emotion}(${getEmotionHue(emotion)}°): ${intensity.toFixed(3)}`).join(', '));

  for (const [emotion, intensity] of topEmotions) {
    const hue = getEmotionHue(emotion);
    const weight = intensity;

    // Handle hue wrapping (e.g., blending 350° and 10°)
    let adjustedHue = hue;
    if (weightedHueSum > 0) {
      const avgHue = weightedHueSum / totalWeight;
      const diff = Math.abs(hue - avgHue);
      if (diff > 180) {
        adjustedHue = hue > avgHue ? hue - 360 : hue + 360;
      }
    }

    weightedHueSum += adjustedHue * weight;
    totalWeight += weight;
  }

  let blendedHue = weightedHueSum / totalWeight;

  // Normalize to 0-360 range
  while (blendedHue < 0) blendedHue += 360;
  while (blendedHue >= 360) blendedHue -= 360;

  const finalHue = Math.round(blendedHue);
  console.log(`🎨 blendEmotionHues: Final blended hue: ${finalHue}°`);
  return finalHue;
}

/**
 * Get emotion intensity for orb effects with improved normalization
 */
export function getEmotionIntensity(emotions: Record<string, number>): number {
  if (!emotions || Object.keys(emotions).length === 0) {
    return 0.3; // Default low intensity
  }

  // Normalize emotions first
  const normalizedEmotions = normalizeEmotionIntensities(emotions);
  const values = Object.values(normalizedEmotions);

  if (values.length === 0) {
    return 0.3;
  }

  const maxIntensity = Math.max(...values);
  const avgIntensity = values.reduce((sum, val) => sum + val, 0) / values.length;

  // Combine max and average for more nuanced intensity
  const combinedIntensity = (maxIntensity * 0.7 + avgIntensity * 0.3);

  // Enhanced scaling for better visual effects
  // Map from normalized range to visual range (0.25 - 0.8)
  const scaledIntensity = 0.25 + (combinedIntensity * 0.55);

  return Math.max(0.25, Math.min(0.8, scaledIntensity));
}

/**
 * Get the dominant emotion name for state-specific effects
 */
export function getDominantEmotion(emotions: Record<string, number>): string {
  if (!emotions || Object.keys(emotions).length === 0) {
    console.log(`🎯 getDominantEmotion: No emotions, returning 'default'`);
    return 'default';
  }

  // Use normalized emotions for better detection
  const normalizedEmotions = normalizeEmotionIntensities(emotions);

  let dominantEmotion = 'default';
  let maxIntensity = 0;

  for (const [emotion, intensity] of Object.entries(normalizedEmotions)) {
    if (intensity > maxIntensity) {
      maxIntensity = intensity;
      dominantEmotion = emotion;
    }
  }

  const result = maxIntensity > 0.05 ? dominantEmotion : 'default';
  console.log(`🎯 getDominantEmotion: ${dominantEmotion} (${maxIntensity.toFixed(3)}) → ${result}`);
  return result;
}
