
import { <PERSON>sponsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON>xis, CartesianGrid, Tooltip, Legend, LineChart, Line } from 'recharts'

interface UserCohort {
  cohortId: string
  cohortName: string
  userCount: number
  retentionRate: number
  avgSessionDuration: number
  avgEmotionScore: number
  characteristics: Record<string, any>
}

interface UserCohortChartProps {
  data: UserCohort[]
  className?: string
}

export function UserCohortChart({ data, className = '' }: UserCohortChartProps) {
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const cohort = data.find(c => c.cohortName === label)
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-900">{label}</p>
          <p className="text-sm text-blue-600">
            Users: <span className="font-medium">{cohort?.userCount}</span>
          </p>
          <p className="text-sm text-green-600">
            Retention: <span className="font-medium">{cohort?.retentionRate.toFixed(1)}%</span>
          </p>
          <p className="text-sm text-purple-600">
            Avg Duration: <span className="font-medium">{cohort?.avgSessionDuration.toFixed(1)} min</span>
          </p>
          <p className="text-sm text-orange-600">
            Emotion Score: <span className="font-medium">{cohort?.avgEmotionScore.toFixed(2)}</span>
          </p>
        </div>
      )
    }
    return null
  }

  return (
    <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">User Cohort Analysis</h3>
        <p className="text-sm text-gray-600">
          Track user behavior and retention across different signup periods
        </p>
      </div>

      {/* User Count and Retention Chart */}
      <div className="mb-8">
        <h4 className="text-md font-medium text-gray-800 mb-4">User Count & Retention Rate</h4>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
              <XAxis 
                dataKey="cohortName" 
                angle={-45}
                textAnchor="end"
                height={80}
                fontSize={12}
              />
              <YAxis yAxisId="left" orientation="left" />
              <YAxis yAxisId="right" orientation="right" />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Bar 
                yAxisId="left"
                dataKey="userCount" 
                fill="#3B82F6" 
                name="User Count"
                radius={[2, 2, 0, 0]}
              />
              <Bar 
                yAxisId="right"
                dataKey="retentionRate" 
                fill="#10B981" 
                name="Retention Rate (%)"
                radius={[2, 2, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Engagement Metrics Chart */}
      <div>
        <h4 className="text-md font-medium text-gray-800 mb-4">Engagement Metrics</h4>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
              <XAxis 
                dataKey="cohortName" 
                angle={-45}
                textAnchor="end"
                height={80}
                fontSize={12}
              />
              <YAxis yAxisId="left" orientation="left" />
              <YAxis yAxisId="right" orientation="right" />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Line 
                yAxisId="left"
                type="monotone" 
                dataKey="avgSessionDuration" 
                stroke="#8B5CF6" 
                strokeWidth={3}
                name="Avg Session Duration (min)"
                dot={{ fill: '#8B5CF6', strokeWidth: 2, r: 4 }}
              />
              <Line 
                yAxisId="right"
                type="monotone" 
                dataKey="avgEmotionScore" 
                stroke="#F59E0B" 
                strokeWidth={3}
                name="Avg Emotion Score"
                dot={{ fill: '#F59E0B', strokeWidth: 2, r: 4 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Cohort Summary Table */}
      <div className="mt-6">
        <h4 className="text-md font-medium text-gray-800 mb-4">Cohort Summary</h4>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Cohort
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Users
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Retention
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Avg Duration
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Emotion Score
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {data.map((cohort) => (
                <tr key={cohort.cohortId} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {cohort.cohortName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {cohort.userCount.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      cohort.retentionRate >= 70 ? 'bg-green-100 text-green-800' :
                      cohort.retentionRate >= 50 ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {cohort.retentionRate.toFixed(1)}%
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {cohort.avgSessionDuration.toFixed(1)} min
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {cohort.avgEmotionScore.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      cohort.characteristics.isRecent ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {cohort.characteristics.isRecent ? 'Recent' : 'Mature'}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
