import { <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON>lt<PERSON>, Area<PERSON>hart, Area } from 'recharts'
import { TrendingUp, TrendingDown, Minus } from 'lucide-react'

interface EmotionTrend {
  emotion: string
  trend: 'rising' | 'falling' | 'stable'
  change: number
}

interface EmotionPeak {
  emotion: string
  peakTime: Date
  intensity: number
}

interface EmotionTransition {
  from: string
  to: string
  frequency: number
  avgTimeDiff: number
}

interface EmotionFlowData {
  currentTrends: EmotionTrend[]
  peakEmotions: EmotionPeak[]
  emotionTransitions: EmotionTransition[]
}

interface EmotionFlowChartProps {
  data: EmotionFlowData
  className?: string
}

export function EmotionFlowChart({ data, className = '' }: EmotionFlowChartProps) {
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'rising':
        return <TrendingUp className="w-4 h-4 text-green-500" />
      case 'falling':
        return <TrendingDown className="w-4 h-4 text-red-500" />
      default:
        return <Minus className="w-4 h-4 text-gray-500" />
    }
  }

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'rising':
        return 'bg-green-100 text-green-800'
      case 'falling':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // Prepare data for transition flow chart
  const transitionData = data.emotionTransitions.map(t => ({
    name: `${t.from} → ${t.to}`,
    frequency: t.frequency,
    avgTime: t.avgTimeDiff / 60, // Convert to minutes
    from: t.from,
    to: t.to
  }))

  // Prepare peak emotions for timeline
  const peakData = data.peakEmotions
    .sort((a, b) => new Date(a.peakTime).getTime() - new Date(b.peakTime).getTime())
    .map(peak => ({
      time: new Date(peak.peakTime).toLocaleTimeString(),
      emotion: peak.emotion,
      intensity: peak.intensity,
      timestamp: peak.peakTime
    }))

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-900">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: <span className="font-medium">{entry.value}</span>
            </p>
          ))}
        </div>
      )
    }
    return null
  }

  return (
    <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Real-Time Emotion Flow</h3>
        <p className="text-sm text-gray-600">
          Monitor emotion trends, peaks, and transitions in real-time
        </p>
      </div>

      {/* Current Trends */}
      <div className="mb-8">
        <h4 className="text-md font-medium text-gray-800 mb-4">Current Emotion Trends</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {data.currentTrends.slice(0, 6).map((trend, index) => (
            <div key={index} className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium text-gray-900 capitalize">{trend.emotion}</span>
                {getTrendIcon(trend.trend)}
              </div>
              <div className="flex items-center justify-between">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTrendColor(trend.trend)}`}>
                  {trend.trend}
                </span>
                <span className="text-sm text-gray-600">
                  {trend.change > 0 ? '+' : ''}{(trend.change * 100).toFixed(1)}%
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Peak Emotions Timeline */}
      <div className="mb-8">
        <h4 className="text-md font-medium text-gray-800 mb-4">Emotion Peaks Timeline</h4>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={peakData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
              <XAxis 
                dataKey="time" 
                fontSize={12}
                angle={-45}
                textAnchor="end"
                height={60}
              />
              <YAxis />
              <Tooltip content={<CustomTooltip />} />
              <Area 
                type="monotone" 
                dataKey="intensity" 
                stroke="#8B5CF6" 
                fill="#8B5CF6" 
                fillOpacity={0.3}
                name="Intensity"
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Emotion Transitions */}
      <div className="mb-6">
        <h4 className="text-md font-medium text-gray-800 mb-4">Most Common Emotion Transitions</h4>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={transitionData.slice(0, 10)} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
              <XAxis 
                dataKey="name" 
                fontSize={10}
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis />
              <Tooltip content={<CustomTooltip />} />
              <Area 
                type="monotone" 
                dataKey="frequency" 
                stroke="#F59E0B" 
                fill="#F59E0B" 
                fillOpacity={0.3}
                name="Frequency"
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Transition Details Table */}
      <div>
        <h4 className="text-md font-medium text-gray-800 mb-4">Transition Details</h4>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  From → To
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Frequency
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Avg Time
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Pattern
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {data.emotionTransitions.slice(0, 8).map((transition, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    <span className="capitalize">{transition.from}</span>
                    <span className="mx-2 text-gray-400">→</span>
                    <span className="capitalize">{transition.to}</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {transition.frequency}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {(transition.avgTimeDiff / 60).toFixed(1)} min
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      transition.frequency >= 20 ? 'bg-red-100 text-red-800' :
                      transition.frequency >= 10 ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {transition.frequency >= 20 ? 'Very Common' :
                       transition.frequency >= 10 ? 'Common' : 'Occasional'}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
