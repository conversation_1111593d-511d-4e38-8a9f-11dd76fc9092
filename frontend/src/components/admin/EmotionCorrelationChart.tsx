
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>tesianG<PERSON>, <PERSON><PERSON><PERSON>, Cell } from 'recharts'

interface EmotionCorrelation {
  emotion1: string
  emotion2: string
  correlation: number
  cooccurrenceCount: number
  significance: number
}

interface EmotionCorrelationChartProps {
  data: EmotionCorrelation[]
  className?: string
}

export function EmotionCorrelationChart({ data, className = '' }: EmotionCorrelationChartProps) {
  // Transform data for scatter plot
  const chartData = data.map((item, index) => ({
    x: item.correlation,
    y: item.cooccurrenceCount,
    correlation: item.correlation,
    cooccurrenceCount: item.cooccurrenceCount,
    significance: item.significance,
    label: `${item.emotion1} ↔ ${item.emotion2}`,
    emotion1: item.emotion1,
    emotion2: item.emotion2,
    index
  }))

  // Color based on correlation strength
  const getColor = (correlation: number) => {
    if (correlation > 0.7) return '#10B981' // Strong positive - green
    if (correlation > 0.3) return '#3B82F6' // Moderate positive - blue
    if (correlation > -0.3) return '#6B7280' // Weak - gray
    if (correlation > -0.7) return '#F59E0B' // Moderate negative - orange
    return '#EF4444' // Strong negative - red
  }

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-900">{data.label}</p>
          <p className="text-sm text-gray-600">
            Correlation: <span className="font-medium">{data.correlation.toFixed(3)}</span>
          </p>
          <p className="text-sm text-gray-600">
            Co-occurrences: <span className="font-medium">{data.cooccurrenceCount}</span>
          </p>
          <p className="text-sm text-gray-600">
            Significance: <span className="font-medium">{(data.significance * 100).toFixed(1)}%</span>
          </p>
        </div>
      )
    }
    return null
  }

  return (
    <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Emotion Correlations</h3>
        <p className="text-sm text-gray-600">
          Discover which emotions tend to appear together in conversations
        </p>
      </div>

      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <ScatterChart data={chartData} margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
            <XAxis 
              type="number" 
              dataKey="x" 
              domain={[-1, 1]}
              tickFormatter={(value) => value.toFixed(1)}
              label={{ value: 'Correlation Strength', position: 'insideBottom', offset: -10 }}
            />
            <YAxis 
              type="number" 
              dataKey="y"
              label={{ value: 'Co-occurrences', angle: -90, position: 'insideLeft' }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Scatter dataKey="y">
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={getColor(entry.correlation)} />
              ))}
            </Scatter>
          </ScatterChart>
        </ResponsiveContainer>
      </div>

      <div className="mt-4 flex flex-wrap gap-4 text-xs">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-green-500 rounded"></div>
          <span>Strong Positive (0.7+)</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-blue-500 rounded"></div>
          <span>Moderate Positive (0.3-0.7)</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-gray-500 rounded"></div>
          <span>Weak (-0.3-0.3)</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-orange-500 rounded"></div>
          <span>Moderate Negative (-0.7--0.3)</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-red-500 rounded"></div>
          <span>Strong Negative (-0.7-)</span>
        </div>
      </div>
    </div>
  )
}
