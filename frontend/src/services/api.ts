import axios, { AxiosInstance } from 'axios'
import type { ApiResponse, User, AuthTokens } from '@shared/types'

class ApiService {
  private api: AxiosInstance
  private baseURL: string

  constructor() {
    this.baseURL = import.meta.env.VITE_API_URL || 'http://localhost:3001'
    
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    this.setupInterceptors()
  }

  private setupInterceptors() {
    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('accessToken')
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => Promise.reject(error)
    )

    // Response interceptor to handle token refresh
    this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true

          try {
            const refreshToken = localStorage.getItem('refreshToken')
            if (refreshToken) {
              const response = await this.refreshToken(refreshToken)
              const { accessToken } = response.data?.tokens || {}

              if (accessToken) {
                localStorage.setItem('accessToken', accessToken)
              }
              originalRequest.headers.Authorization = `Bearer ${accessToken}`
              
              return this.api(originalRequest)
            }
          } catch (refreshError) {
            // Refresh failed, redirect to login
            this.clearTokens()
            window.location.href = '/login'
          }
        }

        return Promise.reject(error)
      }
    )
  }

  // Auth methods
  async verifyGoogleToken(idToken: string): Promise<ApiResponse<{ user: User; tokens: AuthTokens }>> {
    const response = await this.api.post('/api/auth/google/verify', { idToken })
    return response.data
  }

  async refreshToken(refreshToken: string): Promise<ApiResponse<{ tokens: AuthTokens }>> {
    const response = await this.api.post('/api/auth/refresh', { refreshToken })
    return response.data
  }

  async getCurrentUser(): Promise<ApiResponse<{ user: User; stats: any }>> {
    const response = await this.api.get('/api/auth/me')
    return response.data
  }

  async updateProfile(data: { name?: string; profileData?: any }): Promise<ApiResponse<{ user: User }>> {
    const response = await this.api.put('/api/auth/profile', data)
    return response.data
  }

  async logout(): Promise<ApiResponse<{ message: string }>> {
    const response = await this.api.post('/api/auth/logout')
    return response.data
  }

  // Chat methods
  async getChatSessions(limit = 50, offset = 0): Promise<ApiResponse<{ sessions: any[] }>> {
    const response = await this.api.get(`/api/chat/sessions?limit=${limit}&offset=${offset}`)
    return response.data
  }

  async getChatSession(sessionId: string): Promise<ApiResponse<any>> {
    const response = await this.api.get(`/api/chat/sessions/${sessionId}`)
    return response.data
  }

  async getChatMessages(sessionId: string, limit = 100, offset = 0): Promise<ApiResponse<{ messages: any[] }>> {
    const response = await this.api.get(`/api/chat/sessions/${sessionId}/messages?limit=${limit}&offset=${offset}`)
    return response.data
  }

  async getChatTranscript(sessionId: string): Promise<ApiResponse<{ transcript: string }>> {
    const response = await this.api.get(`/api/chat/sessions/${sessionId}/transcript`)
    return response.data
  }

  async getChatEmotions(sessionId: string): Promise<ApiResponse<any>> {
    const response = await this.api.get(`/api/chat/sessions/${sessionId}/emotions`)
    return response.data
  }

  async deleteChatSession(sessionId: string): Promise<ApiResponse<{ message: string }>> {
    const response = await this.api.delete(`/api/chat/sessions/${sessionId}`)
    return response.data
  }

  async getChatAnalytics(days = 30): Promise<ApiResponse<any>> {
    const response = await this.api.get(`/api/chat/analytics?days=${days}`)
    return response.data
  }

  async searchMessages(sessionId: string, query: string, limit = 20): Promise<ApiResponse<{ messages: any[]; query: string }>> {
    const response = await this.api.post(`/api/chat/sessions/${sessionId}/search`, { query, limit })
    return response.data
  }

  // Admin methods
  async getAdminAnalytics(days = 30): Promise<ApiResponse<any>> {
    const response = await this.api.get(`/api/admin/analytics?days=${days}`)
    return response.data
  }

  async getAdminUsers(page = 1, limit = 50): Promise<ApiResponse<any>> {
    const response = await this.api.get(`/api/admin/users?page=${page}&limit=${limit}`)
    return response.data
  }

  async getAdminSessions(page = 1, limit = 50, status?: string): Promise<ApiResponse<any>> {
    const statusParam = status ? `&status=${status}` : ''
    const response = await this.api.get(`/api/admin/sessions?page=${page}&limit=${limit}${statusParam}`)
    return response.data
  }

  async getSystemHealth(): Promise<ApiResponse<any>> {
    const response = await this.api.get('/api/admin/system/health')
    return response.data
  }

  // Admin whitelist management
  async getAdminWhitelist(includeInactive = false): Promise<ApiResponse<any>> {
    const response = await this.api.get(`/api/admin/whitelist?includeInactive=${includeInactive}`)
    return response.data
  }

  async addAdminToWhitelist(adminData: {
    email: string
    role?: string
    permissions?: Record<string, any>
    notes?: string
  }): Promise<ApiResponse<any>> {
    const response = await this.api.post('/api/admin/whitelist', adminData)
    return response.data
  }

  async updateAdminInWhitelist(email: string, updateData: {
    role?: string
    isActive?: boolean
    permissions?: Record<string, any>
    notes?: string
  }): Promise<ApiResponse<any>> {
    const response = await this.api.put(`/api/admin/whitelist/${encodeURIComponent(email)}`, updateData)
    return response.data
  }

  async removeAdminFromWhitelist(email: string): Promise<ApiResponse<any>> {
    const response = await this.api.delete(`/api/admin/whitelist/${encodeURIComponent(email)}`)
    return response.data
  }

  // Admin activity log
  async getAdminActivityLog(filters: {
    adminEmail?: string
    action?: string
    resource?: string
    startDate?: string
    endDate?: string
    page?: number
    limit?: number
  } = {}): Promise<ApiResponse<any>> {
    const params = new URLSearchParams()
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString())
      }
    })

    const response = await this.api.get(`/api/admin/activity-log?${params.toString()}`)
    return response.data
  }

  // Advanced Analytics
  async getEmotionCorrelations(days = 30): Promise<ApiResponse<any>> {
    const response = await this.api.get(`/api/admin/analytics/emotions/correlations?days=${days}`)
    return response.data
  }

  async getUserCohorts(): Promise<ApiResponse<any>> {
    const response = await this.api.get('/api/admin/analytics/cohorts')
    return response.data
  }

  async getEmotionAnomalies(days = 7): Promise<ApiResponse<any>> {
    const response = await this.api.get(`/api/admin/analytics/anomalies?days=${days}`)
    return response.data
  }

  async getPredictiveInsights(): Promise<ApiResponse<any>> {
    const response = await this.api.get('/api/admin/analytics/predictions')
    return response.data
  }

  async getEmotionFlowAnalysis(hours = 24): Promise<ApiResponse<any>> {
    const response = await this.api.get(`/api/admin/analytics/emotion-flow?hours=${hours}`)
    return response.data
  }

  async getUserSegmentation(): Promise<ApiResponse<any>> {
    const response = await this.api.get('/api/admin/analytics/segmentation')
    return response.data
  }

  // WebSocket status
  async getWebSocketStatus(): Promise<ApiResponse<any>> {
    const response = await this.api.get('/api/ws/status')
    return response.data
  }

  // Utility methods
  setTokens(tokens: AuthTokens) {
    localStorage.setItem('accessToken', tokens.accessToken)
    if (tokens.refreshToken) {
      localStorage.setItem('refreshToken', tokens.refreshToken)
    }
  }

  clearTokens() {
    localStorage.removeItem('accessToken')
    localStorage.removeItem('refreshToken')
  }

  getAccessToken(): string | null {
    return localStorage.getItem('accessToken')
  }

  getRefreshToken(): string | null {
    return localStorage.getItem('refreshToken')
  }

  // Google OAuth URL
  getGoogleAuthUrl(): string {
    return `${this.baseURL}/api/auth/google`
  }

  // WebSocket URL
  getWebSocketUrl(token: string): string {
    const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:3001'
    const fullUrl = `${wsUrl}/ws/chat?token=${encodeURIComponent(token)}`
    console.log('🔌 Generated WebSocket URL:', fullUrl)
    return fullUrl
  }
}

export const apiService = new ApiService()
export default apiService
